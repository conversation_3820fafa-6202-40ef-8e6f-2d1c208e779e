services:
  server:
    build: .
    image: parameter-server
    container_name: parameter-server
    ports:
      - "${WEB_PORT}:${WEB_PORT}"
      - "${TCP_PORT}:${TCP_PORT}"
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./static:/app/static
      - ./src:/app/src
      - ./.env:/app/.env
    environment:
      - TZ=${TIMEZONE}
      - WEB_PORT=${WEB_PORT}
      - TCP_PORT=${TCP_PORT}
      - LOG_MAX_SIZE_MB=${LOG_MAX_SIZE_MB}
      - LOG_BACKUP_COUNT=${LOG_BACKUP_COUNT}
      - DATA_DIR=${DATA_DIR}
      - CLIENT_DATA_FILE=${CLIENT_DATA_FILE}
      - TIMEZONE=${TIMEZONE}
    restart: always
    networks:
      - parameter-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${WEB_PORT}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  parameter-network:
    driver: bridge
