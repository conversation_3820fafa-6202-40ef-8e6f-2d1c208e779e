<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志管理 - RK3588参数设置客户端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .back-btn {
            position: absolute;
            left: 30px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: 2px solid rgba(255,255,255,0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
            border-color: rgba(255,255,255,0.5);
            transform: translateY(-50%) translateX(-5px);
        }

        .content {
            padding: 30px;
        }



        .actions {
            display: flex;
            gap: 15px;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(79, 172, 254, 0.4);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
            color: #2d3436;
        }

        .btn-warning:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(255, 234, 167, 0.4);
        }

        .backup-files {
            margin-top: 30px;
        }

        .backup-files h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.3em;
        }

        .file-list {
            background: #f8f9fa;
            border-radius: 10px;
            overflow: hidden;
            max-height: 350px; /* 限制最大高度，大约5个文件项的高度 */
            overflow-y: auto; /* 垂直滚动条 */
            border: 1px solid #e9ecef;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            border-bottom: 1px solid #e9ecef;
            transition: background 0.2s ease;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-item:hover {
            background: #e9ecef;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
        }

        .file-details {
            font-size: 0.9em;
            color: #666;
        }

        .loading {
            text-align: center;
            padding: 40px;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .hidden {
            display: none;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-weight: 500;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .editable {
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
            position: relative;
        }

        .editable:hover {
            background: #e9ecef;
            color: #4facfe;
        }

        .editable::after {
            content: "✏️";
            font-size: 0.8em;
            opacity: 0;
            margin-left: 8px;
            transition: opacity 0.2s ease;
        }

        .editable:hover::after {
            opacity: 1;
        }

        .edit-input {
            width: 100%;
            padding: 4px 8px;
            border: 2px solid #4facfe;
            border-radius: 4px;
            font-size: 1.1em;
            outline: none;
        }

        .edit-select {
            width: 100%;
            padding: 4px 8px;
            border: 2px solid #4facfe;
            border-radius: 4px;
            font-size: 1.1em;
            outline: none;
            background: white;
        }

        .log-content-section {
            margin-top: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border-left: 5px solid #28a745;
        }

        .log-content-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .log-file-info {
            flex: 1;
        }

        .log-file-info span:first-child {
            font-weight: bold;
            color: #333;
            font-size: 1.1em;
        }

        .log-file-details {
            display: block;
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .log-content-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .lines-select {
            padding: 6px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            font-size: 14px;
        }

        .log-content-container {
            background: #2d3748;
            border-radius: 8px;
            padding: 20px;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #e2e8f0;
        }

        .log-content-text {
            color: #e2e8f0;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .file-item {
            cursor: pointer;
        }

        .file-item:hover {
            background: #e9ecef;
        }

        .file-item.active {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }

        /* 自定义滚动条样式 */
        .file-list::-webkit-scrollbar {
            width: 8px;
        }

        .file-list::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .file-list::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
            transition: background 0.2s ease;
        }

        .file-list::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 为火狐浏览器提供滚动条样式 */
        .file-list {
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }

        .scroll-hint {
            font-size: 0.8em;
            color: #666;
            font-weight: normal;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2em;
            }

            .back-btn {
                position: static;
                transform: none;
                margin-bottom: 20px;
                align-self: flex-start;
            }

            .header {
                text-align: left;
            }



            .actions {
                flex-direction: column;
            }

            .file-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <a href="/" class="back-btn">
                ← 返回主页
            </a>
            <h1>📋 日志管理</h1>
            <p>管理系统日志文件和轮转设置</p>
        </div>

        <div class="content">
            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>正在加载日志信息...</p>
            </div>

            <div id="alert-container"></div>

            <div id="log-content" class="hidden">
                <div class="actions" style="margin-bottom: 20px; text-align: center;">
                    <button class="btn btn-primary" id="refresh-btn">
                        🔄 刷新信息
                    </button>
                    <button class="btn btn-warning" id="rotate-btn">
                        🔄 手动轮转
                    </button>
                </div>

                <div class="backup-files">
                    <h3>📁 所有日志文件 <span class="scroll-hint" id="scroll-hint" style="display: none;">↕️ 可滚动查看更多</span></h3>
                    <div class="file-list" id="backup-file-list">
                        <!-- 日志文件列表将在这里动态生成 -->
                    </div>
                </div>

                <div class="log-content-section" id="log-content-section" style="display: none;">
                    <h3>📄 日志内容</h3>
                    <div class="log-content-header">
                        <div class="log-file-info">
                            <span id="current-log-filename">-</span>
                            <span class="log-file-details" id="log-file-details">-</span>
                        </div>
                        <div class="log-content-controls">
                            <select id="lines-select" class="lines-select">
                                <option value="50">最后50行</option>
                                <option value="100" selected>最后100行</option>
                                <option value="200">最后200行</option>
                                <option value="500">最后500行</option>
                                <option value="0">全部内容</option>
                            </select>
                            <button id="refresh-log-btn" class="btn btn-small">🔄 刷新</button>
                            <button id="close-log-btn" class="btn btn-small">✖️ 关闭</button>
                        </div>
                    </div>
                    <div class="log-content-container">
                        <pre id="log-content-text" class="log-content-text">正在加载日志内容...</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 简化的日志管理器
        document.addEventListener('DOMContentLoaded', async () => {
            console.log('日志管理页面加载');

            try {
                // 获取日志信息
                const response = await fetch('/api/log-info');
                const result = await response.json();

                console.log('日志信息:', result);

                if (result.success) {
                    // 隐藏loading
                    const loading = document.getElementById('loading');
                    const content = document.getElementById('log-content');

                    loading.style.display = 'none';
                    content.style.display = 'block';

                    // 获取数据
                    const data = result.data;

                    // 显示所有日志文件
                    const backupList = document.getElementById('backup-file-list');
                    if (backupList) {
                        let filesHtml = '';

                        // 显示所有日志文件
                        if (data.all_log_files && data.all_log_files.length > 0) {
                            data.all_log_files.forEach(file => {
                                let fileIcon = '📄';
                                let fileDescription = '日志文件';

                                // 根据文件名设置不同的图标和描述
                                if (file.filename.includes('connection')) {
                                    fileIcon = '🔗';
                                    fileDescription = '客户端连接日志';
                                } else if (file.filename.includes('server')) {
                                    fileIcon = '🖥️';
                                    fileDescription = '服务器运行日志';
                                } else if (file.filename.includes('test')) {
                                    fileIcon = '🧪';
                                    fileDescription = '测试日志';
                                }

                                // 判断是否为当前主日志文件
                                const isCurrentLog = data.current_log_file && data.current_log_file.includes(file.filename);
                                const currentLogLabel = isCurrentLog ? ' (当前活动)' : '';

                                filesHtml += `
                                    <div class="file-item" data-filename="${file.filename}" onclick="showLogContent('${file.filename}')" style="cursor: pointer; background-color: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 12px; margin-bottom: 8px;">
                                        <div class="file-info">
                                            <div class="file-name" style="font-weight: bold; color: #495057;">${fileIcon} ${file.filename}${currentLogLabel}</div>
                                            <div class="file-details" style="color: #6c757d; font-size: 0.9em;">
                                                ${fileDescription} | 大小: ${file.size_mb.toFixed(2)} MB | 修改时间: ${file.modified_time} | 点击查看内容
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });
                        } else {
                            filesHtml = `
                                <div class="file-item" style="padding: 12px; text-align: center; color: #6c757d;">
                                    <div class="file-info">
                                        <div class="file-name">暂无日志文件</div>
                                        <div class="file-details">系统尚未生成日志文件</div>
                                    </div>
                                </div>
                            `;
                        }

                        backupList.innerHTML = filesHtml;
                    }
                } else {
                    throw new Error(result.message || '获取日志信息失败');
                }
            } catch (error) {
                console.error('加载日志信息失败:', error);
                const loading = document.getElementById('loading');
                loading.innerHTML = `<p style="color: red;">加载失败: ${error.message}</p>`;
            }

            // 绑定返回按钮
            const backBtn = document.querySelector('.back-btn');
            if (backBtn) {
                backBtn.addEventListener('click', () => {
                    window.location.href = '/';
                });
            }

            // 绑定刷新按钮
            const refreshBtn = document.getElementById('refresh-btn');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', () => {
                    window.location.reload();
                });
            }

            // 绑定手动轮转按钮
            const rotateBtn = document.getElementById('rotate-btn');
            if (rotateBtn) {
                rotateBtn.addEventListener('click', async () => {
                    try {
                        console.log('执行手动轮转');

                        // 显示加载状态
                        rotateBtn.disabled = true;
                        rotateBtn.innerHTML = '⏳ 轮转中...';

                        const response = await fetch('/api/log-rotate', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });

                        const result = await response.json();

                        if (result.success) {
                            alert('✅ 日志轮转执行成功！新的日志文件已创建。');
                            // 延迟刷新页面，让轮转操作完成
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        } else {
                            throw new Error(result.message || '日志轮转失败');
                        }
                    } catch (error) {
                        console.error('日志轮转失败:', error);
                        alert(`❌ 日志轮转失败: ${error.message}`);
                    } finally {
                        // 恢复按钮状态
                        rotateBtn.disabled = false;
                        rotateBtn.innerHTML = '🔄 手动轮转';
                    }
                });
            }
        });

        // 显示日志内容的函数
        async function showLogContent(filename) {
            try {
                console.log('显示日志内容:', filename);

                // 高亮选中的文件
                document.querySelectorAll('.file-item').forEach(item => {
                    item.style.backgroundColor = '#f8f9fa';
                });
                document.querySelector(`[data-filename="${filename}"]`).style.backgroundColor = '#e3f2fd';

                // 获取日志内容
                const response = await fetch(`/api/log-content/${filename}?lines=100`);
                const result = await response.json();

                if (result.success) {
                    const data = result.data;

                    // 创建或更新日志内容显示区域
                    let contentSection = document.getElementById('log-content-display');
                    if (!contentSection) {
                        contentSection = document.createElement('div');
                        contentSection.id = 'log-content-display';
                        contentSection.style.cssText = `
                            margin-top: 20px;
                            padding: 20px;
                            background-color: #f8f9fa;
                            border-radius: 8px;
                            border: 1px solid #dee2e6;
                        `;
                        document.getElementById('log-content').appendChild(contentSection);
                    }

                    // 处理连接日志内容，添加颜色图标
                    let processedContent = data.content || '日志文件为空';
                    if (filename.includes('connection') && data.content) {
                        // 为连接日志添加颜色图标
                        processedContent = data.content
                            .replace(/客户端上线/g, '<span style="color: #28a745;">🟢 客户端上线</span>')
                            .replace(/客户端下线/g, '<span style="color: #dc3545;">🔴 客户端下线</span>')
                            .replace(/心跳超时/g, '<span style="color: #ffc107;">⚠️ 心跳超时</span>')
                            .replace(/客户端替换/g, '<span style="color: #17a2b8;">🔄 客户端替换</span>')
                            .replace(/连接错误/g, '<span style="color: #dc3545;">❌ 连接错误</span>')
                            .replace(/客户端清理/g, '<span style="color: #6c757d;">🧹 客户端清理</span>');
                    }

                    contentSection.innerHTML = `
                        <h3>📄 ${filename}</h3>
                        <p style="color: #6c757d; margin-bottom: 15px;">
                            文件大小: ${(data.file_size / 1024 / 1024).toFixed(2)} MB |
                            总行数: ${data.total_lines} |
                            显示: ${data.displayed_lines} 行
                        </p>
                        <pre style="
                            background-color: #2d3748;
                            color: #e2e8f0;
                            padding: 15px;
                            border-radius: 6px;
                            overflow-x: auto;
                            max-height: 400px;
                            overflow-y: auto;
                            font-family: 'Courier New', monospace;
                            font-size: 12px;
                            line-height: 1.4;
                            white-space: pre-wrap;
                        ">${processedContent}</pre>
                    `;

                    // 滚动到内容区域
                    contentSection.scrollIntoView({ behavior: 'smooth' });
                } else {
                    alert(`加载日志内容失败: ${result.message}`);
                }
            } catch (error) {
                console.error('显示日志内容失败:', error);
                alert(`显示日志内容失败: ${error.message}`);
            }
        }
    </script>
</body>
</html>
