// 日志管理页面JavaScript

class LogManager {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadLogInfo();
    }

    bindEvents() {
        // 刷新按钮
        document.getElementById('refresh-btn').addEventListener('click', () => {
            this.loadLogInfo();
        });

        // 手动轮转按钮
        document.getElementById('rotate-btn').addEventListener('click', () => {
            this.rotateLog();
        });

        // 可编辑字段点击事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('editable')) {
                this.startEdit(e.target);
            }
        });

        // 日志内容控制按钮
        document.getElementById('refresh-log-btn').addEventListener('click', () => {
            this.refreshLogContent();
        });

        document.getElementById('close-log-btn').addEventListener('click', () => {
            this.closeLogContent();
        });

        document.getElementById('lines-select').addEventListener('change', () => {
            this.refreshLogContent();
        });
    }

    async loadLogInfo(silent = false) {
        try {
            this.showLoading(true);
            if (!silent) {
                this.clearAlert();
            }

            const response = await fetch('/api/log-info');
            const result = await response.json();

            if (result.success) {
                this.displayLogInfo(result.data);
                if (!silent) {
                    this.showAlert('日志信息加载成功', 'success');
                }
            } else {
                throw new Error(result.message || '获取日志信息失败');
            }
        } catch (error) {
            console.error('加载日志信息失败:', error);
            this.showAlert(`加载日志信息失败: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    displayLogInfo(data) {
        // 更新基本信息
        document.getElementById('current-log-file').textContent = data.current_log_file || '-';
        document.getElementById('current-size').textContent = `${data.current_size_mb?.toFixed(2) || 0} MB`;

        // 可编辑字段，存储原始数据
        const maxSizeEl = document.getElementById('max-size');
        maxSizeEl.textContent = `${data.max_size_mb?.toFixed(1) || 0} MB`;
        maxSizeEl.dataset.value = data.max_size_mb || 0;

        const backupCountEl = document.getElementById('backup-count');
        backupCountEl.textContent = data.backup_count || 0;
        backupCountEl.dataset.value = data.backup_count || 0;



        document.getElementById('usage-percent').textContent = `${data.usage_percent?.toFixed(1) || 0}%`;

        // 更新进度条
        const progressBar = document.getElementById('usage-progress');
        const usagePercent = data.usage_percent || 0;
        progressBar.style.width = `${Math.min(usagePercent, 100)}%`;

        // 根据使用率改变进度条颜色
        if (usagePercent > 80) {
            progressBar.style.background = 'linear-gradient(90deg, #ff6b6b, #ee5a52)';
        } else if (usagePercent > 60) {
            progressBar.style.background = 'linear-gradient(90deg, #feca57, #ff9ff3)';
        } else {
            progressBar.style.background = 'linear-gradient(90deg, #4facfe, #00f2fe)';
        }

        // 更新备份文件列表
        this.displayBackupFiles(data.backup_files || []);
    }

    displayBackupFiles(backupFiles) {
        const container = document.getElementById('backup-file-list');

        // 获取当前日志文件名（从路径中提取文件名）
        const currentLogFile = document.getElementById('current-log-file').textContent;
        const currentLogFileName = currentLogFile.split('/').pop() || currentLogFile;

        // 添加当前日志文件到列表顶部
        let filesHtml = `
            <div class="file-item" data-filename="${currentLogFileName}" onclick="globalLogManager.showLogContent('${currentLogFileName}')">
                <div class="file-info">
                    <div class="file-name">📄 ${currentLogFileName} (当前日志)</div>
                    <div class="file-details">
                        正在使用的日志文件 | 点击查看内容
                    </div>
                </div>
            </div>
        `;

        if (backupFiles.length === 0) {
            filesHtml += `
                <div class="file-item">
                    <div class="file-info">
                        <div class="file-name">暂无备份文件</div>
                        <div class="file-details">当前日志文件尚未达到轮转条件</div>
                    </div>
                </div>
            `;
        } else {
            filesHtml += backupFiles.map(file => `
                <div class="file-item" data-filename="${file.filename}" onclick="globalLogManager.showLogContent('${file.filename}')">
                    <div class="file-info">
                        <div class="file-name">📄 ${file.filename}</div>
                        <div class="file-details">
                            大小: ${file.size_mb.toFixed(2)} MB |
                            修改时间: ${file.modified_time} | 点击查看内容
                        </div>
                    </div>
                </div>
            `).join('');
        }

        container.innerHTML = filesHtml;

        // 检查是否需要显示滚动提示
        this.checkScrollHint(container);
    }

    async rotateLog() {
        try {
            this.showLoading(true);
            this.clearAlert();

            const response = await fetch('/api/log-rotate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert('日志轮转执行成功！新的日志文件已创建。', 'success');
                // 延迟刷新信息，让轮转操作完成
                setTimeout(() => {
                    this.loadLogInfo();
                }, 1000);
            } else {
                throw new Error(result.message || '日志轮转失败');
            }
        } catch (error) {
            console.error('日志轮转失败:', error);
            this.showAlert(`日志轮转失败: ${error.message}`, 'error');
        } finally {
            this.showLoading(false);
        }
    }

    showLoading(show) {
        const loading = document.getElementById('loading');
        const content = document.getElementById('log-content');

        if (show) {
            loading.classList.remove('hidden');
            content.classList.add('hidden');
        } else {
            loading.classList.add('hidden');
            content.classList.remove('hidden');
        }
    }

    showAlert(message, type = 'success') {
        const container = document.getElementById('alert-container');
        const alertClass = type === 'success' ? 'alert-success' : 'alert-error';

        container.innerHTML = `
            <div class="alert ${alertClass}">
                ${type === 'success' ? '✅' : '❌'} ${message}
            </div>
        `;

        // 3秒后自动隐藏成功消息
        if (type === 'success') {
            setTimeout(() => {
                this.clearAlert();
            }, 3000);
        }
    }

    clearAlert() {
        document.getElementById('alert-container').innerHTML = '';
    }

    startEdit(element) {
        const field = element.dataset.field;
        const currentValue = element.dataset.value;

        // 防止重复编辑
        if (element.querySelector('input') || element.querySelector('select')) {
            return;
        }

        let inputElement;

        {
            // 其他字段使用输入框
            inputElement = document.createElement('input');
            inputElement.className = 'edit-input';
            inputElement.type = field === 'max_size_mb' ? 'number' : 'number';
            inputElement.value = field === 'max_size_mb' ? parseFloat(currentValue) : parseInt(currentValue);

            if (field === 'max_size_mb') {
                inputElement.min = '0.1';
                inputElement.max = '1000';
                inputElement.step = '0.1';
            } else if (field === 'backup_count') {
                inputElement.min = '0';
                inputElement.max = '100';
                inputElement.step = '1';
            }
        }

        // 保存原始内容
        const originalContent = element.innerHTML;

        // 替换为输入框
        element.innerHTML = '';
        element.appendChild(inputElement);
        inputElement.focus();

        // 防止重复保存的标志
        let isSaving = false;

        // 处理保存和取消
        const saveEdit = async () => {
            if (isSaving) return; // 防止重复调用
            isSaving = true;

            const newValue = inputElement.value;
            if (await this.saveConfig(field, newValue)) {
                // 保存成功，静默重新加载数据（不显示加载成功消息）
                this.loadLogInfo(true);
            } else {
                // 保存失败，恢复原始内容
                element.innerHTML = originalContent;
            }
        };

        const cancelEdit = () => {
            if (isSaving) return; // 防止在保存过程中取消
            element.innerHTML = originalContent;
        };

        // 事件监听
        inputElement.addEventListener('blur', () => {
            // 延迟执行，避免与keydown冲突
            setTimeout(saveEdit, 100);
        });

        inputElement.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                inputElement.blur(); // 触发blur事件来保存
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    async saveConfig(field, value) {
        try {
            const config = {};
            config[field] = parseFloat(value);

            const response = await fetch('/api/log-config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });

            const result = await response.json();

            if (result.success) {
                this.showAlert(`配置更新成功: ${this.getFieldName(field)}`, 'success');
                return true;
            } else {
                this.showAlert(`配置更新失败: ${result.message}`, 'error');
                return false;
            }
        } catch (error) {
            console.error('保存配置失败:', error);
            this.showAlert(`保存配置失败: ${error.message}`, 'error');
            return false;
        }
    }

    getFieldName(field) {
        const names = {
            'max_size_mb': '最大文件大小',
            'backup_count': '备份文件数量'
        };
        return names[field] || field;
    }

    async showLogContent(filename) {
        try {
            // 显示日志内容区域
            const section = document.getElementById('log-content-section');
            section.style.display = 'block';

            // 更新文件名显示
            document.getElementById('current-log-filename').textContent = filename;

            // 高亮选中的文件
            document.querySelectorAll('.file-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector(`[data-filename="${filename}"]`)?.classList.add('active');

            // 加载日志内容
            await this.loadLogContent(filename);

            // 滚动到日志内容区域
            section.scrollIntoView({ behavior: 'smooth' });

        } catch (error) {
            console.error('显示日志内容失败:', error);
            this.showAlert(`显示日志内容失败: ${error.message}`, 'error');
        }
    }

    async loadLogContent(filename) {
        try {
            const lines = document.getElementById('lines-select').value;
            const contentElement = document.getElementById('log-content-text');

            contentElement.textContent = '正在加载日志内容...';

            const response = await fetch(`/api/log-content/${filename}?lines=${lines}`);
            const result = await response.json();

            if (result.success) {
                const data = result.data;

                // 更新文件详情
                document.getElementById('log-file-details').textContent =
                    `文件大小: ${this.formatFileSize(data.file_size)} | 总行数: ${data.total_lines} | 显示: ${data.displayed_lines} 行`;

                // 显示日志内容
                contentElement.textContent = data.content || '日志文件为空';

                // 滚动到底部（显示最新内容）
                contentElement.scrollTop = contentElement.scrollHeight;

            } else {
                contentElement.textContent = `加载失败: ${result.message}`;
                this.showAlert(`加载日志内容失败: ${result.message}`, 'error');
            }

        } catch (error) {
            console.error('加载日志内容失败:', error);
            document.getElementById('log-content-text').textContent = `加载失败: ${error.message}`;
            this.showAlert(`加载日志内容失败: ${error.message}`, 'error');
        }
    }

    refreshLogContent() {
        const filename = document.getElementById('current-log-filename').textContent;
        if (filename && filename !== '-') {
            this.loadLogContent(filename);
        }
    }

    closeLogContent() {
        document.getElementById('log-content-section').style.display = 'none';

        // 取消文件高亮
        document.querySelectorAll('.file-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    checkScrollHint(container) {
        const scrollHint = document.getElementById('scroll-hint');
        if (!scrollHint) return;

        // 检查内容是否超出容器高度
        if (container.scrollHeight > container.clientHeight) {
            scrollHint.style.display = 'inline';

            // 3秒后自动隐藏提示
            setTimeout(() => {
                scrollHint.style.display = 'none';
            }, 3000);
        } else {
            scrollHint.style.display = 'none';
        }
    }

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 格式化时间
    formatTime(timestamp) {
        return new Date(timestamp).toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }
}

// 全局日志管理器实例
let globalLogManager = null;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    globalLogManager = new LogManager();
});

// 定期刷新日志信息（每30秒）
setInterval(() => {
    if (document.visibilityState === 'visible' && globalLogManager) {
        globalLogManager.loadLogInfo(true); // 静默刷新，不显示成功消息
    }
}, 30000);
