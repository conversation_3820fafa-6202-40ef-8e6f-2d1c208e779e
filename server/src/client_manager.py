#!/usr/bin/env python3
"""
客户端管理器
负责客户端信息的存储、管理和持久化
"""

import json
import time
import os
from datetime import datetime
from typing import Dict, List, Optional

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .log_manager import get_log_manager
except ImportError:
    from log_manager import get_log_manager

class ClientManager:
    def __init__(self, data_file: str = "data/clients.json"):
        self.data_file = data_file
        self.clients: Dict[str, dict] = {}
        self.log_manager = get_log_manager()
        self.load_clients()

    def add_client(self, client_info: dict) -> bool:
        """添加客户端"""
        try:
            client_id = client_info['device_id']
            client_ip = client_info['ip']
            web_port = client_info.get('web_port', 8000)
            server_type = client_info.get('server_type')

            # 检查是否有相同IP的客户端，如果有则覆盖
            existing_client_id = None
            for existing_id, existing_client in self.clients.items():
                if existing_client['ip'] == client_ip:
                    existing_client_id = existing_id
                    break

            # 保存钉钉通知设置（在删除旧记录之前）
            existing_dingtalk_enabled = False
            if existing_client_id and existing_client_id != client_id:
                # 不同客户端但相同IP，保留旧客户端的钉钉设置
                existing_dingtalk_enabled = self.clients[existing_client_id].get('dingtalk_enabled', False)
                print(f"发现相同IP的客户端 {existing_client_id}，将被新客户端 {client_id} 覆盖")
                # 记录客户端替换日志
                self.connection_logger.log_client_replaced(existing_client_id, client_id, client_ip)
                del self.clients[existing_client_id]
            elif existing_client_id == client_id:
                # 相同客户端重复连接，保留其钉钉设置
                existing_dingtalk_enabled = self.clients[client_id].get('dingtalk_enabled', False)
            elif client_id in self.clients:
                # 客户端已存在（不同IP），保留其钉钉设置
                existing_dingtalk_enabled = self.clients[client_id].get('dingtalk_enabled', False)

            self.clients[client_id] = {
                'device_id': client_id,
                'ip': client_ip,
                'web_port': web_port,
                'status': 'online',
                'connected_at': datetime.now().isoformat(),
                'last_heartbeat': time.time(),
                'dingtalk_enabled': existing_dingtalk_enabled  # 保留或默认为False
            }
            self.save_clients()

            # 记录客户端连接日志
            self.log_manager.log_client_connect(client_id, client_ip, web_port)

            print(f"客户端上线: {client_id} ({client_ip})")
            return True
        except Exception as e:
            error_msg = f"添加客户端失败: {e}"
            print(error_msg)
            # 记录连接错误日志
            if 'client_id' in locals():
                self.log_manager.log_connection_error(client_id, str(e))
            return False

    def remove_client(self, client_id: str) -> bool:
        """移除客户端（完全删除记录）"""
        try:
            if client_id in self.clients:
                del self.clients[client_id]
                self.save_clients()

                # 记录客户端删除日志
                self.connection_logger.log_client_offline(client_id, "客户端记录被删除")
                print(f"客户端已删除: {client_id}")
                return True
            return False
        except Exception as e:
            error_msg = f"移除客户端失败: {e}"
            print(error_msg)
            self.connection_logger.log_connection_error(client_id, str(e))
            return False

    def set_client_offline(self, client_id: str, reason: str = "正常断开") -> bool:
        """设置客户端为离线状态"""
        try:
            if client_id in self.clients:
                self.clients[client_id]['status'] = 'offline'
                self.save_clients()

                # 记录客户端断开日志
                self.log_manager.log_client_disconnect(client_id, reason)

                print(f"客户端下线: {client_id}")
                return True
            return False
        except Exception as e:
            error_msg = f"设置客户端离线失败: {e}"
            print(error_msg)
            self.log_manager.log_connection_error(client_id, str(e))
            return False

    def update_heartbeat(self, client_id: str) -> bool:
        """更新客户端心跳时间"""
        try:
            if client_id in self.clients:
                self.clients[client_id]['last_heartbeat'] = time.time()
                self.clients[client_id]['status'] = 'online'
                # 保存到文件
                self.save_clients()
                return True
            return False
        except Exception as e:
            print(f"更新心跳失败: {e}")
            return False

    def get_all_clients(self) -> List[dict]:
        """获取所有客户端信息"""
        try:
            # 重新加载最新数据
            self.load_clients()

            # 检查心跳超时（60秒）
            current_time = time.time()
            for client in self.clients.values():
                last_heartbeat = client.get('last_heartbeat', 0)
                if current_time - last_heartbeat > 60 and client['status'] == 'online':
                    client['status'] = 'offline'
                    # 记录心跳超时日志
                    last_heartbeat_str = datetime.fromtimestamp(last_heartbeat).strftime('%Y-%m-%d %H:%M:%S')
                    self.log_manager.log_heartbeat_timeout(client['device_id'], last_heartbeat_str)

            # 生成Web链接
            result = []
            for client in self.clients.values():
                client_data = client.copy()
                if client['status'] == 'online':
                    client_data['web_url'] = f"http://{client['ip']}:{client['web_port']}"
                else:
                    client_data['web_url'] = None
                result.append(client_data)

            return result
        except Exception as e:
            print(f"获取客户端列表失败: {e}")
            return []

    def get_client(self, client_id: str) -> Optional[dict]:
        """获取单个客户端信息"""
        return self.clients.get(client_id)

    def update_client_dingtalk_status(self, client_id: str, enabled: bool) -> bool:
        """更新客户端钉钉通知状态"""
        try:
            if client_id in self.clients:
                self.clients[client_id]['dingtalk_enabled'] = enabled
                self.save_clients()
                print(f"客户端 {client_id} 钉钉通知状态更新为: {'启用' if enabled else '禁用'}")
                return True
            return False
        except Exception as e:
            print(f"更新客户端钉钉通知状态失败: {e}")
            return False

    def save_clients(self) -> bool:
        """保存客户端信息到文件"""
        try:
            # 确保数据目录存在
            os.makedirs(os.path.dirname(self.data_file), exist_ok=True)

            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.clients, f, indent=2, ensure_ascii=False)
            print(f"保存了 {len(self.clients)} 个客户端记录到文件")
            return True
        except Exception as e:
            print(f"保存客户端数据失败: {e}")
            return False

    def load_clients(self) -> bool:
        """从文件加载客户端信息"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    self.clients = json.load(f)
                print(f"加载了 {len(self.clients)} 个客户端记录")
            else:
                self.clients = {}
                print("客户端数据文件不存在，创建新的")
            return True
        except Exception as e:
            print(f"加载客户端数据失败: {e}")
            self.clients = {}
            return False

    def get_online_count(self) -> int:
        """获取在线客户端数量"""
        return sum(1 for client in self.clients.values() if client['status'] == 'online')

    def cleanup_offline_clients(self, days: int = 0) -> int:
        """清理离线的客户端记录"""
        try:
            current_time = time.time()
            cutoff_time = current_time - (days * 24 * 3600)

            to_remove = []
            for client_id, client in self.clients.items():
                if client['status'] == 'offline':
                    if days == 0:
                        # 清理所有离线客户端
                        to_remove.append(client_id)
                    elif client.get('last_heartbeat', 0) < cutoff_time:
                        # 清理长时间离线的客户端
                        to_remove.append(client_id)

            for client_id in to_remove:
                del self.clients[client_id]

            if to_remove:
                self.save_clients()
                # 记录客户端清理日志
                reason = "清理所有离线客户端" if days == 0 else f"清理{days}天前离线的客户端"
                self.connection_logger.log_client_cleanup(to_remove, reason)

                if days == 0:
                    print(f"清理了 {len(to_remove)} 个离线客户端")
                else:
                    print(f"清理了 {len(to_remove)} 个长时间离线的客户端")

            return len(to_remove)
        except Exception as e:
            print(f"清理离线客户端失败: {e}")
            return 0
