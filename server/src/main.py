#!/usr/bin/env python3
"""
服务器主程序
启动Web服务器和TCP服务器，集成日志管理和配置管理
"""

import asyncio
import sys
import os
import signal
import logging
from datetime import datetime
from typing import Optional

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from web_server import start_web_server, set_global_config_manager, set_global_log_manager, set_global_client_manager
from tcp_server import start_tcp_server
from log_manager import setup_logging, get_log_manager
from config_manager import setup_config_manager
from client_manager import ClientManager
from dingtalk_service import init_dingtalk_service


class ServerApplication:
    """服务器应用程序"""

    def __init__(self):
        """初始化服务器应用"""
        # 首先设置配置管理器
        self.config_manager = setup_config_manager()

        # 然后设置日志管理器
        log_config = self.config_manager.get_config()
        log_file = "logs/server.log"
        max_bytes = int(float(log_config.get('LOG_MAX_SIZE_MB', '10')) * 1024 * 1024)
        backup_count = int(log_config.get('LOG_BACKUP_COUNT', '10'))

        self.log_manager = setup_logging(
            log_file=log_file,
            max_bytes=max_bytes,
            backup_count=backup_count
        )

        self.logger = self.log_manager.get_logger(__name__)

        # 创建共享的客户端管理器
        self.client_manager = ClientManager()

        # 设置全局引用
        set_global_config_manager(self.config_manager)
        set_global_log_manager(self.log_manager)
        set_global_client_manager(self.client_manager)

        # 初始化钉钉服务
        self.init_dingtalk_service()

        # 组件
        self.web_server_task: Optional[asyncio.Task] = None
        self.tcp_server_task: Optional[asyncio.Task] = None

        # 运行状态
        self.running = False
        self.shutdown_event = asyncio.Event()

        # 设置信号处理
        self.setup_signal_handlers()

    def init_dingtalk_service(self):
        """初始化钉钉服务"""
        try:
            config = self.config_manager.get_config()
            dingtalk_enabled = config.get('DINGTALK_ENABLED', 'false').lower() == 'true'

            if dingtalk_enabled:
                webhook_url = config.get('DINGTALK_WEBHOOK_URL', '')
                secret = config.get('DINGTALK_SECRET', '')

                if webhook_url:
                    init_dingtalk_service(webhook_url, secret)
                    self.logger.info("钉钉通知服务初始化成功")
                else:
                    self.logger.warning("钉钉服务已启用但未配置Webhook URL")
            else:
                self.logger.info("钉钉通知服务未启用")

        except Exception as e:
            self.logger.error(f"初始化钉钉服务失败: {e}")

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"收到信号 {signum}，开始关闭服务器...")
            asyncio.create_task(self.shutdown())

        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    async def start_web_server(self):
        """启动Web服务器"""
        try:
            self.logger.info("启动Web服务器...")
            await start_web_server()
        except Exception as e:
            self.logger.error(f"Web服务器运行错误: {e}")
            await self.shutdown()

    async def start_tcp_server(self):
        """启动TCP服务器"""
        try:
            self.logger.info("启动TCP服务器...")
            await start_tcp_server(self.client_manager)
        except Exception as e:
            self.logger.error(f"TCP服务器运行错误: {e}")
            await self.shutdown()

    async def health_monitor(self):
        """健康监控"""
        while self.running:
            try:
                # 检查各组件状态
                web_server_alive = self.web_server_task and not self.web_server_task.done()
                tcp_server_alive = self.tcp_server_task and not self.tcp_server_task.done()

                if not web_server_alive:
                    self.logger.warning("Web服务器任务已停止")

                if not tcp_server_alive:
                    self.logger.warning("TCP服务器任务已停止")

                # 记录状态信息
                config = self.config_manager.get_config()
                self.logger.debug(f"服务器状态 - Web端口: {config['WEB_PORT']}, TCP端口: {config['TCP_PORT']}")

                await asyncio.sleep(60)  # 每分钟检查一次

            except Exception as e:
                self.logger.error(f"健康监控错误: {e}")
                await asyncio.sleep(10)

    async def start(self):
        """启动服务器应用"""
        self.running = True
        config = self.config_manager.get_config()

        self.logger.info("=" * 50)
        self.logger.info("参数设置系统服务器启动")
        self.logger.info(f"Web端口: {config['WEB_PORT']}")
        self.logger.info(f"TCP端口: {config['TCP_PORT']}")
        self.logger.info(f"钉钉通知: {'启用' if config['DINGTALK_ENABLED'].lower() == 'true' else '禁用'}")
        self.logger.info("=" * 50)

        # 记录服务器启动
        self.log_manager.log_server_startup(
            server_port=config['WEB_PORT'],
            client_port=config['TCP_PORT']
        )

        try:
            # 创建任务
            self.web_server_task = asyncio.create_task(
                self.start_web_server(),
                name="web_server"
            )

            self.tcp_server_task = asyncio.create_task(
                self.start_tcp_server(),
                name="tcp_server"
            )

            health_monitor_task = asyncio.create_task(
                self.health_monitor(),
                name="health_monitor"
            )

            # 等待关闭信号或任务完成
            done, pending = await asyncio.wait(
                [
                    self.web_server_task,
                    self.tcp_server_task,
                    health_monitor_task,
                    asyncio.create_task(self.shutdown_event.wait())
                ],
                return_when=asyncio.FIRST_COMPLETED
            )

            # 取消未完成的任务
            for task in pending:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

            self.logger.info("所有任务已完成")

        except Exception as e:
            self.logger.error(f"应用运行错误: {e}")
        finally:
            await self.cleanup()

    async def shutdown(self):
        """关闭应用"""
        if not self.running:
            return

        self.logger.info("开始关闭服务器...")
        self.running = False
        self.shutdown_event.set()

        self.logger.info("服务器关闭完成")

    async def cleanup(self):
        """清理资源"""
        self.logger.info("清理资源...")

        # 确保所有任务都已取消
        tasks = [self.web_server_task, self.tcp_server_task]
        for task in tasks:
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass

        self.logger.info("资源清理完成")


async def main():
    """主函数"""
    print("参数设置系统服务器启动中...")

    # 创建并启动应用
    app = ServerApplication()

    try:
        await app.start()
    except KeyboardInterrupt:
        print("\n用户中断程序")
    except Exception as e:
        print(f"程序运行错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("服务器退出")


if __name__ == "__main__":
    # 设置事件循环策略（Linux下可能需要）
    if sys.platform.startswith('linux'):
        try:
            import uvloop
            asyncio.set_event_loop_policy(uvloop.EventLoopPolicy())
        except ImportError:
            pass

    # 运行主程序
    asyncio.run(main())
