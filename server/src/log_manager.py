#!/usr/bin/env python3
"""
服务器端日志管理器 - 简化版本
专门记录：客户端连接、参数修改、服务器启动时间戳等关键操作
支持日志轮转、自动清理和配置更新
"""

import os
import logging
import logging.handlers
from datetime import datetime
from typing import Optional


class ServerLogManager:
    """服务器日志管理器 - 简化版本"""

    def __init__(self, log_file: str = "logs/server.log",
                 max_bytes: int = 10 * 1024 * 1024,  # 10MB
                 backup_count: int = 10):
        """
        初始化服务器日志管理器

        Args:
            log_file: 日志文件路径
            max_bytes: 单个日志文件最大大小（字节）
            backup_count: 保留的备份文件数量
        """
        self.log_file = log_file
        self.max_bytes = max_bytes
        self.backup_count = backup_count

        # 创建日志目录
        os.makedirs(os.path.dirname(log_file), exist_ok=True)

        # 设置日志格式 - 简洁但包含必要信息
        self.formatter = logging.Formatter('%(asctime)s | %(levelname)s | %(message)s')

        # 初始化日志记录器
        self.logger = None
        self._setup_logging()

    def _setup_logging(self):
        """设置日志记录器"""
        # 创建专用的日志记录器
        self.logger = logging.getLogger('server_logger')
        self.logger.setLevel(logging.INFO)  # 固定使用INFO级别

        # 清除现有的处理器
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)

        # 创建时间戳轮转文件处理器
        file_handler = TimestampRotatingFileHandler(
            filename=self.log_file,
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        file_handler.setFormatter(self.formatter)
        self.logger.addHandler(file_handler)

        # 添加控制台处理器用于重要信息
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(self.formatter)
        console_handler.setLevel(logging.INFO)  # 控制台只显示INFO及以上级别
        self.logger.addHandler(console_handler)

        # 防止日志传播到根日志记录器
        self.logger.propagate = False

    def log_server_startup(self, server_port: int, client_port: int = None):
        """记录服务器启动"""
        if client_port:
            self.logger.info(f"🚀 服务器启动 | Web端口: {server_port} | 客户端端口: {client_port}")
        else:
            self.logger.info(f"🚀 服务器启动 | Web端口: {server_port}")

    def log_client_connect(self, client_id: str, client_ip: str, web_port: int = None):
        """记录客户端连接"""
        if web_port:
            self.logger.info(f"🟢 客户端上线 | ID: {client_id} | IP: {client_ip} | Web端口: {web_port}")
        else:
            self.logger.info(f"🟢 客户端上线 | ID: {client_id} | IP: {client_ip}")

    def log_client_disconnect(self, client_id: str, reason: str = "正常断开"):
        """记录客户端断开"""
        self.logger.info(f"🔴 客户端下线 | ID: {client_id} | 原因: {reason}")

    def log_parameter_request(self, client_id: str, operation: str, param_info: str = None):
        """记录参数操作请求"""
        if param_info:
            self.logger.info(f"⚙️ 参数操作 | 客户端: {client_id} | 操作: {operation} | 参数: {param_info}")
        else:
            self.logger.info(f"⚙️ 参数操作 | 客户端: {client_id} | 操作: {operation}")

    def log_connection_error(self, client_id: str, error_msg: str):
        """记录连接错误"""
        self.logger.error(f"❌ 连接错误 | ID: {client_id} | 错误: {error_msg}")

    def log_heartbeat_timeout(self, client_id: str, last_heartbeat_time: str):
        """记录心跳超时"""
        self.logger.warning(f"⚠️ 心跳超时 | ID: {client_id} | 最后心跳: {last_heartbeat_time}")

    def get_logger(self, name: str = None) -> logging.Logger:
        """获取日志记录器"""
        return logging.getLogger(name) if name else self.logger

    def get_log_info(self) -> dict:
        """获取日志信息"""
        log_info = {
            "current_log_file": self.log_file,
            "max_size_mb": self.max_bytes / 1024 / 1024,
            "backup_count": self.backup_count
        }

        # 获取当前日志文件大小
        if os.path.exists(self.log_file):
            current_size = os.path.getsize(self.log_file)
            log_info["current_size_mb"] = current_size / 1024 / 1024
            log_info["usage_percent"] = (current_size / self.max_bytes) * 100
        else:
            log_info["current_size_mb"] = 0
            log_info["usage_percent"] = 0

        # 获取备份文件列表
        log_dir = os.path.dirname(self.log_file)
        base_name = os.path.splitext(os.path.basename(self.log_file))[0]
        extension = os.path.splitext(self.log_file)[1]

        backup_files = []
        if os.path.exists(log_dir):
            for filename in os.listdir(log_dir):
                if filename.startswith(f"{base_name}_") and filename.endswith(extension):
                    full_path = os.path.join(log_dir, filename)
                    if os.path.isfile(full_path):
                        backup_files.append({
                            "filename": filename,
                            "size_mb": os.path.getsize(full_path) / 1024 / 1024,
                            "modified_time": datetime.fromtimestamp(os.path.getmtime(full_path)).strftime("%Y-%m-%d %H:%M:%S")
                        })

        # 按修改时间排序
        backup_files.sort(key=lambda x: x["modified_time"], reverse=True)
        log_info["backup_files"] = backup_files

        return log_info

    def force_rotate(self):
        """强制执行日志轮转"""
        for handler in self.logger.handlers:
            if isinstance(handler, TimestampRotatingFileHandler):
                handler.doRollover()
                self.logger.info("🔄 手动执行日志轮转")
                break

    def update_config(self, max_size_mb=None, backup_count=None):
        """更新日志配置"""
        try:
            updated = False

            # 更新最大文件大小
            if max_size_mb is not None:
                new_max_bytes = int(max_size_mb * 1024 * 1024)
                if new_max_bytes != self.max_bytes:
                    self.max_bytes = new_max_bytes
                    updated = True
                    self.logger.info(f"更新最大文件大小: {max_size_mb}MB")

            # 更新备份文件数量
            if backup_count is not None:
                if backup_count != self.backup_count:
                    self.backup_count = backup_count
                    updated = True
                    self.logger.info(f"更新备份文件数量: {backup_count}")

            # 如果有更新，重新配置处理器
            if updated:
                self._update_handlers()

            return updated

        except Exception as e:
            import traceback
            self.logger.error(f"更新日志配置失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")
            return False

    def _update_handlers(self):
        """更新日志处理器配置"""
        try:
            self.logger.info(f"开始更新日志处理器，当前处理器数量: {len(self.logger.handlers)}")

            # 找到并更新文件处理器
            handler_found = False
            for i, handler in enumerate(self.logger.handlers):
                self.logger.info(f"处理器 {i}: {type(handler).__name__}")
                if isinstance(handler, TimestampRotatingFileHandler):
                    old_max_bytes = handler.maxBytes
                    old_backup_count = handler.backupCount
                    handler.maxBytes = self.max_bytes
                    handler.backupCount = self.backup_count
                    self.logger.info(f"日志处理器配置已更新: maxBytes {old_max_bytes} -> {self.max_bytes}, backupCount {old_backup_count} -> {self.backup_count}")
                    handler_found = True
                    break

            if not handler_found:
                self.logger.warning("未找到TimestampRotatingFileHandler处理器")

        except Exception as e:
            import traceback
            self.logger.error(f"更新日志处理器失败: {e}")
            self.logger.error(f"错误详情: {traceback.format_exc()}")


class TimestampRotatingFileHandler(logging.handlers.RotatingFileHandler):
    """带时间戳的轮转文件处理器"""

    def __init__(self, filename, mode='a', maxBytes=0, backupCount=0, encoding=None, delay=False):
        """
        初始化轮转文件处理器

        Args:
            filename: 日志文件名
            mode: 文件打开模式
            maxBytes: 最大文件大小（字节），0表示不限制
            backupCount: 保留的备份文件数量
            encoding: 文件编码
            delay: 是否延迟创建文件
        """
        super().__init__(filename, mode, maxBytes, backupCount, encoding, delay)

    def doRollover(self):
        """执行日志轮转"""
        if self.stream:
            self.stream.close()
            self.stream = None

        # 生成带时间戳的备份文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_filename = os.path.splitext(self.baseFilename)[0]
        extension = os.path.splitext(self.baseFilename)[1]

        # 新的备份文件名格式: server_20250730_143022.log
        backup_filename = f"{base_filename}_{timestamp}{extension}"

        # 移动当前日志文件到备份文件
        if os.path.exists(self.baseFilename):
            os.rename(self.baseFilename, backup_filename)

        # 清理旧的备份文件（如果设置了backupCount）
        if self.backupCount > 0:
            self._cleanup_old_backups()

        # 重新打开日志文件
        if not self.delay:
            self.stream = self._open()

    def _cleanup_old_backups(self):
        """清理旧的备份文件"""
        try:
            # 获取日志文件目录
            log_dir = os.path.dirname(self.baseFilename)
            base_name = os.path.splitext(os.path.basename(self.baseFilename))[0]
            extension = os.path.splitext(self.baseFilename)[1]

            # 查找所有备份文件
            backup_files = []
            for filename in os.listdir(log_dir):
                if filename.startswith(f"{base_name}_") and filename.endswith(extension):
                    full_path = os.path.join(log_dir, filename)
                    if os.path.isfile(full_path):
                        backup_files.append((full_path, os.path.getmtime(full_path)))

            # 按修改时间排序，保留最新的backupCount个文件
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # 删除多余的备份文件
            for file_path, _ in backup_files[self.backupCount:]:
                try:
                    os.remove(file_path)
                    print(f"删除旧日志文件: {file_path}")
                except OSError as e:
                    print(f"删除日志文件失败 {file_path}: {e}")

        except Exception as e:
            print(f"清理备份文件时出错: {e}")





# 全局日志管理器实例
_log_manager: Optional[ServerLogManager] = None


def get_log_manager() -> ServerLogManager:
    """获取全局日志管理器实例"""
    global _log_manager
    if _log_manager is None:
        # 如果没有通过setup_logging初始化，使用默认配置
        _log_manager = ServerLogManager()
    return _log_manager


def get_simple_server_log_manager() -> ServerLogManager:
    """获取服务器日志管理器实例（兼容性函数）"""
    return get_log_manager()


def setup_logging(log_file: str = "logs/server.log",
                 max_bytes: int = 10 * 1024 * 1024,
                 backup_count: int = 10) -> ServerLogManager:
    """设置日志系统"""
    global _log_manager
    _log_manager = ServerLogManager(log_file, max_bytes, backup_count)
    return _log_manager


def setup_simple_server_logging(log_file: str = "logs/server.log",
                               max_bytes: int = 10 * 1024 * 1024,
                               backup_count: int = 10) -> ServerLogManager:
    """设置服务器日志系统（兼容性函数）"""
    return setup_logging(log_file, max_bytes, backup_count)