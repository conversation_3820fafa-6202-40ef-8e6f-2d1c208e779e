#!/usr/bin/env python3
"""
TCP服务器
监听客户端连接，处理心跳和连接管理
"""

import socket
import json
import threading
import asyncio
import os
from typing import Dict, Set

from client_manager import ClientManager
from dingtalk_service import send_client_online_notification

class TCPServer:
    def __init__(self, host: str = "0.0.0.0", port: int = 8888, client_manager=None):
        self.host = host
        self.port = port
        self.client_manager = client_manager or ClientManager()
        self.active_connections: Dict[str, socket.socket] = {}
        self.connection_threads: Set[threading.Thread] = set()

    def handle_client(self, conn: socket.socket, addr: tuple):
        """处理单个客户端连接"""
        client_id = None
        try:
            # 设置socket超时
            conn.settimeout(70)  # 70秒超时，比心跳间隔长一些

            # 接收客户端信息
            data = conn.recv(1024)
            if not data:
                return

            client_info = json.loads(data.decode('utf-8'))
            client_id = client_info['device_id']

            print(f"客户端连接: {client_id} from {addr[0]}")

            # 记录客户端
            self.client_manager.add_client(client_info)
            self.active_connections[client_id] = conn

            # 检查客户端钉钉通知开关状态，只有启用时才发送通知
            client_data = self.client_manager.get_client(client_id)
            if client_data and client_data.get('dingtalk_enabled', False):
                send_client_online_notification(client_id, addr[0])
                print(f"已为客户端 {client_id} 发送钉钉上线通知")
            else:
                print(f"客户端 {client_id} 钉钉通知已禁用，跳过发送通知")

            # 发送连接确认响应
            response = {"status": "connected", "message": "连接成功"}
            conn.send(json.dumps(response).encode('utf-8'))

            # 心跳循环
            while True:
                try:
                    # 接收心跳或其他数据
                    heartbeat = conn.recv(1024)
                    if not heartbeat:
                        break

                    # 更新心跳时间
                    if self.client_manager.update_heartbeat(client_id):
                        print(f"收到心跳: {client_id}")
                        # 发送心跳响应
                        conn.send(b"heartbeat_ack")
                    else:
                        print(f"客户端 {client_id} 不存在，断开连接")
                        break

                except socket.timeout:
                    print(f"客户端心跳超时: {client_id}")
                    disconnect_reason = "心跳超时"
                    break
                except Exception as e:
                    print(f"心跳处理错误: {e}")
                    disconnect_reason = f"心跳处理错误: {e}"
                    break

        except json.JSONDecodeError:
            print(f"客户端数据格式错误: {addr}")
            disconnect_reason = "数据格式错误"
        except Exception as e:
            print(f"处理客户端连接错误: {e}")
            disconnect_reason = f"连接处理错误: {e}"
        finally:
            # 清理连接
            if client_id:
                # 使用具体的断开原因
                reason = disconnect_reason if 'disconnect_reason' in locals() else "连接断开"
                self.client_manager.set_client_offline(client_id, reason)
                if client_id in self.active_connections:
                    del self.active_connections[client_id]
                print(f"客户端断开: {client_id}")

            try:
                conn.close()
            except:
                pass

    def start_server(self):
        """启动TCP服务器"""
        try:
            # 创建socket
            server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            server_socket.bind((self.host, self.port))
            server_socket.listen(5)

            print(f"TCP服务器启动，监听 {self.host}:{self.port}")

            while True:
                try:
                    # 接受连接
                    conn, addr = server_socket.accept()
                    print(f"新连接来自: {addr}")

                    # 创建线程处理客户端
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(conn, addr),
                        daemon=True
                    )
                    client_thread.start()
                    self.connection_threads.add(client_thread)

                    # 清理已结束的线程
                    self.connection_threads = {t for t in self.connection_threads if t.is_alive()}

                except Exception as e:
                    print(f"接受连接错误: {e}")

        except Exception as e:
            print(f"TCP服务器启动失败: {e}")
        finally:
            try:
                server_socket.close()
            except:
                pass

    def stop_server(self):
        """停止服务器"""
        print("正在关闭TCP服务器...")

        # 关闭所有活动连接
        for client_id, conn in self.active_connections.items():
            try:
                conn.close()
            except:
                pass

        self.active_connections.clear()
        print("TCP服务器已关闭")

    def get_active_connections_count(self) -> int:
        """获取活动连接数"""
        return len(self.active_connections)

    def broadcast_message(self, message: str) -> int:
        """向所有连接的客户端广播消息"""
        sent_count = 0
        for client_id, conn in list(self.active_connections.items()):
            try:
                conn.send(message.encode('utf-8'))
                sent_count += 1
            except Exception as e:
                print(f"向客户端 {client_id} 发送消息失败: {e}")
                # 移除失效连接
                if client_id in self.active_connections:
                    del self.active_connections[client_id]

        return sent_count

# 全局TCP服务器实例
tcp_server = None

async def start_tcp_server(client_manager=None):
    """启动TCP服务器的异步包装函数"""
    global tcp_server
    host = "0.0.0.0"
    port = int(os.getenv('TCP_PORT', '8888'))
    tcp_server = TCPServer(host, port, client_manager)

    loop = asyncio.get_event_loop()
    await loop.run_in_executor(None, tcp_server.start_server)
