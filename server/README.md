# 参数设置系统服务器端

这是参数设置系统的服务器端程序，用于管理连接的 RK3588 客户端设备，提供 Web 界面显示客户端列表、配置管理、日志管理等功能。

## 🚀 新增功能特性

### 管理功能

- **配置管理**: 通过 Web 界面管理服务器配置，支持实时更新
- **日志管理**: 支持日志文件轮转、时间戳命名、在线查看
- **热重载**: 支持代码和配置的热重载，无需重启服务

### 技术特性

- **异步架构**: 基于 FastAPI 和 asyncio 的高性能异步架构
- **环境变量配置**: 通过.env 文件统一管理配置
- **健康检查**: 内置健康检查和监控功能

## 🚀 快速开始

构建
docker build --no-cache -t parameter-server .

### 配置参数

编辑 `.env` 文件修改配置：

```bash
# Web服务端口
WEB_PORT=8000

# TCP监听端口
TCP_PORT=8888

# 钉钉通知配置
DINGTALK_ENABLED=true
DINGTALK_WEBHOOK_URL=your_webhook_url
DINGTALK_SECRET=your_secret
```

### 启动服务

```bash
# 使用启动脚本
./start.sh

# 或手动启动
docker-compose up -d
```

### 访问界面

- **主界面**: http://localhost:8000
- **配置管理**: http://localhost:8000/config.html
- **日志管理**: http://localhost:8000/log-manager.html
- **健康检查**: http://localhost:8000/health

## 📁 项目结构

```
server/
├── .env                   # 环境配置文件
├── docker-compose.yml     # 容器编排配置
├── Dockerfile            # 容器构建文件
├── requirements.txt      # Python依赖
├── start.sh             # 启动脚本
├── src/                 # 源代码目录
│   ├── main.py         # 主程序入口
│   ├── web_server.py   # Web服务器
│   ├── tcp_server.py   # TCP服务器
│   ├── dingtalk_service.py # 钉钉通知服务
│   ├── client_manager.py # 客户端管理
│   ├── log_manager.py  # 日志管理器
│   └── config_manager.py # 配置管理器
├── static/             # 静态文件
│   ├── index.html      # 客户端列表界面
│   ├── config.html     # 配置管理界面
│   ├── log-manager.html # 日志管理界面
│   ├── style.css       # 样式文件
│   ├── script.js       # JavaScript
│   ├── config.js       # 配置管理JS
│   └── log-manager.js  # 日志管理JS
└── data/               # 数据目录
    └── clients.json    # 客户端数据
```

## ⚙️ 核心功能

- **🌐 Web 界面**: 显示客户端列表和状态
- **🔗 TCP 服务器**: 监听客户端连接和心跳
- **📱 钉钉通知**: 新客户端连接时发送钉钉通知
- **📊 实时监控**: 客户端状态实时更新
- **⚙️ 配置管理**: 在线配置管理，支持实时更新
- **📋 日志管理**: 日志轮转、在线查看、配置调整
- **🔄 热重载**: 代码和配置热重载，无需重启
- **🐳 容器化**: Docker 部署，开机自启动

## 🔧 配置说明

所有配置通过 `.env` 文件管理，修改后重启服务即可生效：

| 配置项               | 说明              | 默认值 |
| -------------------- | ----------------- | ------ |
| WEB_PORT             | Web 服务端口      | 8000   |
| TCP_PORT             | TCP 监听端口      | 8888   |
| DINGTALK_ENABLED     | 是否启用钉钉      | false  |
| DINGTALK_WEBHOOK_URL | 钉钉 Webhook 地址 | -      |
| DINGTALK_SECRET      | 钉钉加签密钥      | -      |

## 🛠️ 常用命令

```bash
# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down

# 重启服务
docker-compose restart
```

## 📊 日志管理

### 日志功能特性

- **自动轮转**: 日志文件大小达到限制时自动轮转
- **时间戳命名**: 备份文件使用时间戳命名（如：server_20250730_143022.log）
- **在线查看**: 通过 Web 界面查看日志内容
- **配置管理**: 支持在线调整日志级别、文件大小等参数

### 日志管理界面

访问 `/log-manager.html` 可以：

- 查看当前日志状态和配置
- 在线查看日志文件内容
- 手动执行日志轮转
- 调整日志配置参数

## ⚙️ 配置管理

### 配置功能特性

- **Web 界面配置**: 通过 `/config.html` 页面在线修改配置
- **实时生效**: 配置修改后立即生效，无需重启
- **配置验证**: 自动验证配置参数的有效性
- **默认值恢复**: 支持一键恢复默认配置

### 完整配置说明

```bash
# Web服务端口
WEB_PORT=8000

# TCP监听端口
TCP_PORT=8888

# 钉钉通知配置
DINGTALK_ENABLED=true
DINGTALK_WEBHOOK_URL=your_webhook_url
DINGTALK_SECRET=your_secret

# 日志配置
LOG_MAX_SIZE_MB=10
LOG_BACKUP_COUNT=10

# 数据存储配置
DATA_DIR=data
CLIENT_DATA_FILE=clients.json

# 系统配置
TIMEZONE=Asia/Shanghai
```

## 🔌 API 接口

### 客户端管理

- `GET /api/clients` - 获取客户端列表
- `GET /api/clients/{client_id}` - 获取单个客户端信息
- `DELETE /api/clients/{client_id}` - 移除客户端
- `GET /api/stats` - 获取统计信息
- `POST /api/cleanup` - 清理离线客户端

### 配置管理

- `GET /api/config` - 获取当前配置
- `POST /api/config` - 保存配置
- `POST /api/config/reset` - 重置为默认配置

### 日志管理

- `GET /api/log-info` - 获取日志信息
- `POST /api/log-rotate` - 手动执行日志轮转
- `POST /api/log-config` - 更新日志配置
- `GET /api/log-content/{filename}` - 获取日志文件内容

### 系统监控

- `GET /health` - 健康检查

## 🔗 客户端连接协议

### 连接协议

客户端通过 TCP 连接到服务器，发送 JSON 格式的连接信息：

```json
{
  "device_id": "RK3588_001",
  "ip": "*************",
  "web_port": 7001,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

服务器响应：

```json
{
  "status": "connected",
  "message": "连接成功"
}
```

### 心跳协议

- 客户端每 30 秒发送心跳
- 服务器超过 60 秒未收到心跳则标记为离线

## 🧪 测试

运行功能测试：

```bash
python test_server.py

# 进入容器调试
docker-compose exec server bash
```

## 📋 API 接口

- `GET /` - 客户端列表界面
- `GET /api/clients` - 获取客户端列表
- `GET /health` - 健康检查

## 🔍 故障排除

### 端口冲突

```bash
# 检查端口占用
netstat -tlnp | grep 8000
```

### 查看详细日志

```bash
# 查看容器内日志
docker-compose exec server tail -f logs/server.log
```

## 📋 系统要求

- Docker 20.10+
- docker-compose 1.29+
- 内存: 至少 256MB
- 网络: 客户端能访问 TCP 端口
